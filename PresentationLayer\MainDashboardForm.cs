using DXApplication1.Utilities;
using DXApplication1.Models;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraNavBar;
using DevExpress.XtraCharts;
using DevExpress.XtraBars.Docking;
using DevExpress.XtraBars.Ribbon;

namespace DXApplication1.PresentationLayer
{
    /// <summary>
    /// نموذج لوحة التحكم الرئيسية - Main Dashboard Form
    /// </summary>
    public partial class MainDashboardForm : RibbonForm
    {
        private User? _currentUser;
        private DevExpress.XtraBars.Ribbon.RibbonControl ribbonControl1;
        private DevExpress.XtraBars.BarButtonItem btnCustomers;
        private DevExpress.XtraBars.BarButtonItem btnProducts;
        private DevExpress.XtraBars.BarButtonItem btnInvoices;
        private DevExpress.XtraBars.BarButtonItem btnReports;
        private DevExpress.XtraBars.BarButtonItem btnUsers;
        private DevExpress.XtraBars.BarButtonItem btnSettings;
        private DevExpress.XtraBars.BarButtonItem btnLogout;
        private DevExpress.XtraBars.BarButtonItem btnRefresh;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraCharts.ChartControl chartControl1;
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraEditors.LabelControl lblTotalSales;
        private DevExpress.XtraEditors.LabelControl lblTotalCustomers;
        private DevExpress.XtraEditors.LabelControl lblTotalProducts;
        private DevExpress.XtraEditors.LabelControl lblTotalInvoices;

        public MainDashboardForm(User currentUser)
        {
            InitializeComponent();
            _currentUser = currentUser;
            SetupForm();
            SetupButtons();
            LoadDashboardData();
        }

        private void InitializeComponent()
        {
            this.ribbonControl1 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.btnCustomers = new DevExpress.XtraBars.BarButtonItem();
            this.btnProducts = new DevExpress.XtraBars.BarButtonItem();
            this.btnInvoices = new DevExpress.XtraBars.BarButtonItem();
            this.btnReports = new DevExpress.XtraBars.BarButtonItem();
            this.btnUsers = new DevExpress.XtraBars.BarButtonItem();
            this.btnSettings = new DevExpress.XtraBars.BarButtonItem();
            this.btnLogout = new DevExpress.XtraBars.BarButtonItem();
            this.btnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.chartControl1 = new DevExpress.XtraCharts.ChartControl();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.lblTotalSales = new DevExpress.XtraEditors.LabelControl();
            this.lblTotalCustomers = new DevExpress.XtraEditors.LabelControl();
            this.lblTotalProducts = new DevExpress.XtraEditors.LabelControl();
            this.lblTotalInvoices = new DevExpress.XtraEditors.LabelControl();

            var ribbonPage1 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            var ribbonPageGroup1 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            var ribbonPageGroup2 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            var ribbonPageGroup3 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();

            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            this.SuspendLayout();

            // ribbonControl1
            this.ribbonControl1.ExpandCollapseItem.Id = 0;
            this.ribbonControl1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
                this.ribbonControl1.ExpandCollapseItem,
                this.btnCustomers,
                this.btnProducts,
                this.btnInvoices,
                this.btnReports,
                this.btnUsers,
                this.btnSettings,
                this.btnLogout,
                this.btnRefresh});
            this.ribbonControl1.Location = new System.Drawing.Point(0, 0);
            this.ribbonControl1.MaxItemId = 9;
            this.ribbonControl1.Name = "ribbonControl1";
            this.ribbonControl1.Pages.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPage[] { ribbonPage1 });
            this.ribbonControl1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.ribbonControl1.Size = new System.Drawing.Size(1200, 158);

            // Ribbon buttons
            this.btnCustomers.Caption = "العملاء";
            this.btnCustomers.Id = 1;
            this.btnCustomers.Name = "btnCustomers";
            this.btnCustomers.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnCustomers_Click);

            this.btnProducts.Caption = "المنتجات";
            this.btnProducts.Id = 2;
            this.btnProducts.Name = "btnProducts";
            this.btnProducts.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnProducts_Click);

            this.btnInvoices.Caption = "الفواتير";
            this.btnInvoices.Id = 3;
            this.btnInvoices.Name = "btnInvoices";
            this.btnInvoices.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnInvoices_Click);

            this.btnReports.Caption = "التقارير";
            this.btnReports.Id = 4;
            this.btnReports.Name = "btnReports";
            this.btnReports.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnReports_Click);

            this.btnUsers.Caption = "المستخدمين";
            this.btnUsers.Id = 5;
            this.btnUsers.Name = "btnUsers";
            this.btnUsers.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnUsers_Click);

            this.btnSettings.Caption = "الإعدادات";
            this.btnSettings.Id = 6;
            this.btnSettings.Name = "btnSettings";
            this.btnSettings.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnSettings_Click);

            this.btnLogout.Caption = "تسجيل الخروج";
            this.btnLogout.Id = 7;
            this.btnLogout.Name = "btnLogout";
            this.btnLogout.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnLogout_Click);

            this.btnRefresh.Caption = "تحديث";
            this.btnRefresh.Id = 8;
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnRefresh_Click);

            // Ribbon page and groups
            ribbonPage1.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
                ribbonPageGroup1, ribbonPageGroup2, ribbonPageGroup3});
            ribbonPage1.Name = "ribbonPage1";
            ribbonPage1.Text = "نظام إدارة المبيعات";

            ribbonPageGroup1.ItemLinks.Add(this.btnCustomers);
            ribbonPageGroup1.ItemLinks.Add(this.btnProducts);
            ribbonPageGroup1.ItemLinks.Add(this.btnInvoices);
            ribbonPageGroup1.Name = "ribbonPageGroup1";
            ribbonPageGroup1.Text = "البيانات الأساسية";

            ribbonPageGroup2.ItemLinks.Add(this.btnReports);
            ribbonPageGroup2.ItemLinks.Add(this.btnUsers);
            ribbonPageGroup2.ItemLinks.Add(this.btnSettings);
            ribbonPageGroup2.Name = "ribbonPageGroup2";
            ribbonPageGroup2.Text = "الإدارة";

            ribbonPageGroup3.ItemLinks.Add(this.btnRefresh);
            ribbonPageGroup3.ItemLinks.Add(this.btnLogout);
            ribbonPageGroup3.Name = "ribbonPageGroup3";
            ribbonPageGroup3.Text = "النظام";

            // panelControl1
            this.panelControl1.Controls.Add(this.chartControl1);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelControl1.Location = new System.Drawing.Point(0, 158);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(1200, 402);
            this.panelControl1.TabIndex = 1;

            // chartControl1
            this.chartControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControl1.Location = new System.Drawing.Point(2, 2);
            this.chartControl1.Name = "chartControl1";
            this.chartControl1.SeriesSerializable = new DevExpress.XtraCharts.Series[0];
            this.chartControl1.Size = new System.Drawing.Size(1196, 398);
            this.chartControl1.TabIndex = 0;

            // panelControl2
            this.panelControl2.Controls.Add(this.lblTotalInvoices);
            this.panelControl2.Controls.Add(this.lblTotalProducts);
            this.panelControl2.Controls.Add(this.lblTotalCustomers);
            this.panelControl2.Controls.Add(this.lblTotalSales);
            this.panelControl2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelControl2.Location = new System.Drawing.Point(0, 560);
            this.panelControl2.Name = "panelControl2";
            this.panelControl2.Size = new System.Drawing.Size(1200, 100);
            this.panelControl2.TabIndex = 2;

            // Labels
            this.lblTotalSales.Appearance.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.lblTotalSales.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.lblTotalSales.Location = new System.Drawing.Point(50, 20);
            this.lblTotalSales.Name = "lblTotalSales";
            this.lblTotalSales.Size = new System.Drawing.Size(150, 21);
            this.lblTotalSales.TabIndex = 0;
            this.lblTotalSales.Text = "إجمالي المبيعات: 0 ر.س";

            this.lblTotalCustomers.Appearance.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.lblTotalCustomers.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.lblTotalCustomers.Location = new System.Drawing.Point(350, 20);
            this.lblTotalCustomers.Name = "lblTotalCustomers";
            this.lblTotalCustomers.Size = new System.Drawing.Size(90, 21);
            this.lblTotalCustomers.TabIndex = 1;
            this.lblTotalCustomers.Text = "عدد العملاء: 0";

            this.lblTotalProducts.Appearance.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.lblTotalProducts.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.lblTotalProducts.Location = new System.Drawing.Point(650, 20);
            this.lblTotalProducts.Name = "lblTotalProducts";
            this.lblTotalProducts.Size = new System.Drawing.Size(100, 21);
            this.lblTotalProducts.TabIndex = 2;
            this.lblTotalProducts.Text = "عدد المنتجات: 0";

            this.lblTotalInvoices.Appearance.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.lblTotalInvoices.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.lblTotalInvoices.Location = new System.Drawing.Point(950, 20);
            this.lblTotalInvoices.Name = "lblTotalInvoices";
            this.lblTotalInvoices.Size = new System.Drawing.Size(95, 21);
            this.lblTotalInvoices.TabIndex = 3;
            this.lblTotalInvoices.Text = "عدد الفواتير: 0";

            // MainDashboardForm
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1200, 660);
            this.Controls.Add(this.panelControl1);
            this.Controls.Add(this.panelControl2);
            this.Controls.Add(this.ribbonControl1);
            this.IconOptions.ShowIcon = false;
            this.Name = "MainDashboardForm";
            this.Ribbon = this.ribbonControl1;
            this.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "نظام إدارة المبيعات";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.MainDashboardForm_FormClosing);

            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            this.panelControl2.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void SetupForm()
        {
            // إعداد النموذج للغة العربية - Setup form for Arabic
            this.Text = $"نظام إدارة المبيعات - مرحباً {_currentUser?.FullName}";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
        }

        private void SetupButtons()
        {
            // إعداد الأزرار حسب صلاحيات المستخدم - Setup buttons based on user permissions
            if (_currentUser?.Role == null) return;

            // إعداد الأزرار حسب الصلاحيات - Setup buttons based on permissions
            btnCustomers.Enabled = _currentUser.Role.CanManageCustomers;
            btnProducts.Enabled = _currentUser.Role.CanManageProducts;
            btnInvoices.Enabled = _currentUser.Role.CanCreateInvoices;
            btnReports.Enabled = _currentUser.Role.CanViewReports;
            btnUsers.Enabled = _currentUser.Role.CanManageUsers;
            btnSettings.Enabled = _currentUser.Role.CanManageSettings;
        }

        private void LoadDashboardData()
        {
            try
            {
                // تحميل بيانات لوحة التحكم - Load dashboard data
                LoadSummaryData();
                LoadRecentActivities();
                LoadChartData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات لوحة التحكم:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadSummaryData()
        {
            // TODO: Load summary statistics
            // عدد العملاء، المنتجات، الفواتير، إجمالي المبيعات
            lblTotalSales.Text = "إجمالي المبيعات: 0 ر.س";
            lblTotalCustomers.Text = "عدد العملاء: 0";
            lblTotalProducts.Text = "عدد المنتجات: 0";
            lblTotalInvoices.Text = "عدد الفواتير: 0";
        }

        private void LoadRecentActivities()
        {
            // TODO: Load recent activities
            // آخر الفواتير، آخر العملاء المضافين، إلخ
        }

        private void LoadChartData()
        {
            // TODO: Load chart data
            // مخططات المبيعات، أداء المنتجات، إلخ
        }

        // معالجات الأحداث - Event Handlers
        private void btnCustomers_Click(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            // فتح نموذج إدارة العملاء - Open customers management form
            var customersForm = new CustomersForm();
            customersForm.ShowDialog();
        }

        private void btnProducts_Click(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            // فتح نموذج إدارة المنتجات - Open products management form
            var productsForm = new ProductsForm();
            productsForm.ShowDialog();
        }

        private void btnInvoices_Click(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            // فتح نموذج إدارة الفواتير - Open invoices management form
            var invoicesForm = new InvoicesForm();
            invoicesForm.ShowDialog();
        }

        private void btnReports_Click(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            // فتح نموذج التقارير - Open reports form
            var reportsForm = new ReportsForm();
            reportsForm.ShowDialog();
        }

        private void btnUsers_Click(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            // فتح نموذج إدارة المستخدمين - Open users management form
            var usersForm = new UsersForm();
            usersForm.ShowDialog();
        }

        private void btnSettings_Click(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            // فتح نموذج الإعدادات - Open settings form
            var settingsForm = new SettingsForm();
            settingsForm.ShowDialog();
        }

        private void MainDashboardForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إغلاق النظام؟", "تأكيد الإغلاق",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result != DialogResult.Yes)
            {
                e.Cancel = true;
            }
        }

        private void btnLogout_Click(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد تسجيل الخروج",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void btnRefresh_Click(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadDashboardData();
        }
    }

    // نماذج وهمية للاختبار - Dummy forms for testing
    public class CustomersForm : XtraForm
    {
        public CustomersForm()
        {
            this.Text = "إدارة العملاء";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.IconOptions.ShowIcon = false;
        }
    }

    public class ProductsForm : XtraForm
    {
        public ProductsForm()
        {
            this.Text = "إدارة المنتجات";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.IconOptions.ShowIcon = false;
        }
    }

    public class InvoicesForm : XtraForm
    {
        public InvoicesForm()
        {
            this.Text = "إدارة الفواتير";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.IconOptions.ShowIcon = false;
        }
    }

    public class ReportsForm : XtraForm
    {
        public ReportsForm()
        {
            this.Text = "التقارير";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.IconOptions.ShowIcon = false;
        }
    }

    public class UsersForm : XtraForm
    {
        public UsersForm()
        {
            this.Text = "إدارة المستخدمين";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.IconOptions.ShowIcon = false;
        }
    }

    public class SettingsForm : XtraForm
    {
        public SettingsForm()
        {
            this.Text = "الإعدادات";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(600, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.IconOptions.ShowIcon = false;
        }
    }
}
